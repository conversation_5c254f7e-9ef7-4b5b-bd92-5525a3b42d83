export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      admin_sessions: {
        Row: {
          created_at: string
          expires_at: string
          id: string
          session_id: string
        }
        Insert: {
          created_at?: string
          expires_at?: string
          id?: string
          session_id: string
        }
        Update: {
          created_at?: string
          expires_at?: string
          id?: string
          session_id?: string
        }
        Relationships: []
      }
      feedback: {
        Row: {
          best_part: string | null
          boat_class: string
          coach_feedback: string | null
          coach_quality: number | null
          created_at: string
          enjoyment: number | null
          equipment: number | null
          equipment_quality: number | null
          facilities: number | null
          food_quality: number | null
          future_participation: number | null
          id: string
          improvements: string | null
          instruction_clarity: number | null
          instructions: number | null
          learning: number | null
          meals: number | null
          organization: number | null
          other_comments: string | null
          recommend_to_non_sailors: string | null
          recommendation: number | null
          safety: number | null
          safety_measures: number | null
          social_connections: number | null
          tempo: number | null
          training_date: string
          weather_handling: number | null
        }
        Insert: {
          best_part?: string | null
          boat_class: string
          coach_feedback?: string | null
          coach_quality?: number | null
          created_at?: string
          enjoyment?: number | null
          equipment?: number | null
          equipment_quality?: number | null
          facilities?: number | null
          food_quality?: number | null
          future_participation?: number | null
          id?: string
          improvements?: string | null
          instruction_clarity?: number | null
          instructions?: number | null
          learning?: number | null
          meals?: number | null
          organization?: number | null
          other_comments?: string | null
          recommend_to_non_sailors?: string | null
          recommendation?: number | null
          safety?: number | null
          safety_measures?: number | null
          social_connections?: number | null
          tempo?: number | null
          training_date: string
          weather_handling?: number | null
        }
        Update: {
          best_part?: string | null
          boat_class?: string
          coach_feedback?: string | null
          coach_quality?: number | null
          created_at?: string
          enjoyment?: number | null
          equipment?: number | null
          equipment_quality?: number | null
          facilities?: number | null
          food_quality?: number | null
          future_participation?: number | null
          id?: string
          improvements?: string | null
          instruction_clarity?: number | null
          instructions?: number | null
          learning?: number | null
          meals?: number | null
          organization?: number | null
          other_comments?: string | null
          recommend_to_non_sailors?: string | null
          recommendation?: number | null
          safety?: number | null
          safety_measures?: number | null
          social_connections?: number | null
          tempo?: number | null
          training_date?: string
          weather_handling?: number | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
