// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://slfpmrtgtftzbxnxckzb.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNsZnBtcnRndGZ0emJ4bnhja3piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjYzMDIsImV4cCI6MjA2NDc0MjMwMn0.d5B0ZKxXoTaI2E7xJjqEcLGIaqjYQaxouRrMioKal1Y";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);